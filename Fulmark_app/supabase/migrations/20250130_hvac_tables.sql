-- HVAC System Tables Migration
-- This migration adds HVAC-specific tables to transform the basic CRM into a comprehensive HVAC management system

-- Equipment table for HVAC devices (air conditioners, heat pumps, etc.)
create table "public"."equipment" (
    "id" bigint generated by default as identity not null,
    "created_at" timestamp with time zone not null default now(),
    "updated_at" timestamp with time zone not null default now(),
    "company_id" bigint not null,
    "contact_id" bigint,
    "brand" text not null,
    "model" text not null,
    "serial_number" text,
    "equipment_type" text not null, -- 'air_conditioner', 'heat_pump', 'ventilation', 'other'
    "installation_date" timestamp with time zone,
    "warranty_expiry" timestamp with time zone,
    "location" text, -- room/area where equipment is installed
    "capacity" text, -- cooling/heating capacity
    "refrigerant_type" text, -- R-32, R-410A, etc.
    "status" text not null default 'active', -- 'active', 'inactive', 'needs_service', 'replaced'
    "notes" text,
    "specifications" jsonb, -- technical specifications
    "photos" jsonb[], -- equipment photos
    "sales_id" bigint
);

alter table "public"."equipment" enable row level security;

-- Service Orders table for HVAC service requests
create table "public"."service_orders" (
    "id" bigint generated by default as identity not null,
    "created_at" timestamp with time zone not null default now(),
    "updated_at" timestamp with time zone not null default now(),
    "company_id" bigint not null,
    "contact_id" bigint not null,
    "equipment_id" bigint,
    "title" text not null,
    "description" text,
    "priority" text not null default 'medium', -- 'low', 'medium', 'high', 'urgent'
    "status" text not null default 'new', -- 'new', 'scheduled', 'in_progress', 'completed', 'cancelled'
    "service_type" text not null, -- 'installation', 'maintenance', 'repair', 'inspection'
    "scheduled_date" timestamp with time zone,
    "completed_date" timestamp with time zone,
    "technician_id" bigint,
    "estimated_duration" integer, -- in minutes
    "actual_duration" integer, -- in minutes
    "parts_cost" decimal(10,2),
    "labor_cost" decimal(10,2),
    "total_cost" decimal(10,2),
    "customer_signature" text, -- base64 encoded signature
    "work_performed" text,
    "recommendations" text,
    "photos_before" jsonb[],
    "photos_after" jsonb[],
    "sales_id" bigint,
    "invoice_id" bigint
);

alter table "public"."service_orders" enable row level security;

-- Technicians table for service staff
create table "public"."technicians" (
    "id" bigint generated by default as identity not null,
    "created_at" timestamp with time zone not null default now(),
    "updated_at" timestamp with time zone not null default now(),
    "first_name" text not null,
    "last_name" text not null,
    "email" text not null,
    "phone" text,
    "employee_id" text,
    "specializations" text[], -- array of specializations like 'air_conditioning', 'heat_pumps', etc.
    "certifications" jsonb[], -- certifications with expiry dates
    "status" text not null default 'active', -- 'active', 'inactive', 'on_leave'
    "hourly_rate" decimal(8,2),
    "avatar" jsonb,
    "address" text,
    "emergency_contact" jsonb,
    "notes" text,
    "sales_id" bigint
);

alter table "public"."technicians" enable row level security;

-- Parts Inventory table
create table "public"."parts_inventory" (
    "id" bigint generated by default as identity not null,
    "created_at" timestamp with time zone not null default now(),
    "updated_at" timestamp with time zone not null default now(),
    "part_number" text not null,
    "name" text not null,
    "description" text,
    "category" text not null, -- 'filters', 'refrigerant', 'electrical', 'mechanical', etc.
    "brand" text,
    "compatible_models" text[], -- array of compatible equipment models
    "quantity_in_stock" integer not null default 0,
    "minimum_stock_level" integer not null default 0,
    "unit_cost" decimal(10,2),
    "supplier" text,
    "supplier_part_number" text,
    "location" text, -- warehouse location
    "notes" text
);

alter table "public"."parts_inventory" enable row level security;

-- Service Order Parts junction table
create table "public"."service_order_parts" (
    "id" bigint generated by default as identity not null,
    "service_order_id" bigint not null,
    "part_id" bigint not null,
    "quantity_used" integer not null,
    "unit_cost" decimal(10,2),
    "total_cost" decimal(10,2)
);

alter table "public"."service_order_parts" enable row level security;

-- Maintenance Schedules table
create table "public"."maintenance_schedules" (
    "id" bigint generated by default as identity not null,
    "created_at" timestamp with time zone not null default now(),
    "updated_at" timestamp with time zone not null default now(),
    "equipment_id" bigint not null,
    "schedule_type" text not null, -- 'monthly', 'quarterly', 'semi_annual', 'annual'
    "next_service_date" timestamp with time zone not null,
    "last_service_date" timestamp with time zone,
    "service_description" text,
    "is_active" boolean not null default true,
    "reminder_days_before" integer not null default 7,
    "assigned_technician_id" bigint,
    "notes" text
);

alter table "public"."maintenance_schedules" enable row level security;

-- Invoices table for HVAC billing
create table "public"."invoices" (
    "id" bigint generated by default as identity not null,
    "created_at" timestamp with time zone not null default now(),
    "updated_at" timestamp with time zone not null default now(),
    "invoice_number" text not null unique,
    "company_id" bigint not null,
    "contact_id" bigint not null,
    "service_order_id" bigint,
    "issue_date" timestamp with time zone not null default now(),
    "due_date" timestamp with time zone not null,
    "subtotal" decimal(10,2) not null,
    "tax_amount" decimal(10,2) not null default 0,
    "total_amount" decimal(10,2) not null,
    "status" text not null default 'draft', -- 'draft', 'sent', 'paid', 'overdue', 'cancelled'
    "payment_date" timestamp with time zone,
    "payment_method" text,
    "notes" text,
    "line_items" jsonb not null, -- detailed invoice line items
    "sales_id" bigint
);

alter table "public"."invoices" enable row level security;

-- Create indexes for better performance
CREATE INDEX idx_equipment_company_id ON public.equipment(company_id);
CREATE INDEX idx_equipment_contact_id ON public.equipment(contact_id);
CREATE INDEX idx_equipment_status ON public.equipment(status);
CREATE INDEX idx_service_orders_company_id ON public.service_orders(company_id);
CREATE INDEX idx_service_orders_contact_id ON public.service_orders(contact_id);
CREATE INDEX idx_service_orders_technician_id ON public.service_orders(technician_id);
CREATE INDEX idx_service_orders_status ON public.service_orders(status);
CREATE INDEX idx_service_orders_scheduled_date ON public.service_orders(scheduled_date);
CREATE INDEX idx_technicians_status ON public.technicians(status);
CREATE INDEX idx_parts_inventory_category ON public.parts_inventory(category);
CREATE INDEX idx_maintenance_schedules_equipment_id ON public.maintenance_schedules(equipment_id);
CREATE INDEX idx_maintenance_schedules_next_service_date ON public.maintenance_schedules(next_service_date);
CREATE INDEX idx_invoices_company_id ON public.invoices(company_id);
CREATE INDEX idx_invoices_status ON public.invoices(status);
CREATE INDEX idx_invoices_due_date ON public.invoices(due_date);

-- Create primary key constraints
CREATE UNIQUE INDEX equipment_pkey ON public.equipment USING btree (id);
CREATE UNIQUE INDEX service_orders_pkey ON public.service_orders USING btree (id);
CREATE UNIQUE INDEX technicians_pkey ON public.technicians USING btree (id);
CREATE UNIQUE INDEX parts_inventory_pkey ON public.parts_inventory USING btree (id);
CREATE UNIQUE INDEX service_order_parts_pkey ON public.service_order_parts USING btree (id);
CREATE UNIQUE INDEX maintenance_schedules_pkey ON public.maintenance_schedules USING btree (id);
CREATE UNIQUE INDEX invoices_pkey ON public.invoices USING btree (id);

alter table "public"."equipment" add constraint "equipment_pkey" PRIMARY KEY using index "equipment_pkey";
alter table "public"."service_orders" add constraint "service_orders_pkey" PRIMARY KEY using index "service_orders_pkey";
alter table "public"."technicians" add constraint "technicians_pkey" PRIMARY KEY using index "technicians_pkey";
alter table "public"."parts_inventory" add constraint "parts_inventory_pkey" PRIMARY KEY using index "parts_inventory_pkey";
alter table "public"."service_order_parts" add constraint "service_order_parts_pkey" PRIMARY KEY using index "service_order_parts_pkey";
alter table "public"."maintenance_schedules" add constraint "maintenance_schedules_pkey" PRIMARY KEY using index "maintenance_schedules_pkey";
alter table "public"."invoices" add constraint "invoices_pkey" PRIMARY KEY using index "invoices_pkey";

-- Add foreign key constraints
alter table "public"."equipment" add constraint "equipment_company_id_fkey" FOREIGN KEY (company_id) REFERENCES companies(id) ON UPDATE CASCADE ON DELETE CASCADE;
alter table "public"."equipment" add constraint "equipment_contact_id_fkey" FOREIGN KEY (contact_id) REFERENCES contacts(id) ON UPDATE CASCADE ON DELETE SET NULL;
alter table "public"."equipment" add constraint "equipment_sales_id_fkey" FOREIGN KEY (sales_id) REFERENCES sales(id) ON UPDATE CASCADE ON DELETE SET NULL;

alter table "public"."service_orders" add constraint "service_orders_company_id_fkey" FOREIGN KEY (company_id) REFERENCES companies(id) ON UPDATE CASCADE ON DELETE CASCADE;
alter table "public"."service_orders" add constraint "service_orders_contact_id_fkey" FOREIGN KEY (contact_id) REFERENCES contacts(id) ON UPDATE CASCADE ON DELETE CASCADE;
alter table "public"."service_orders" add constraint "service_orders_equipment_id_fkey" FOREIGN KEY (equipment_id) REFERENCES equipment(id) ON UPDATE CASCADE ON DELETE SET NULL;
alter table "public"."service_orders" add constraint "service_orders_technician_id_fkey" FOREIGN KEY (technician_id) REFERENCES technicians(id) ON UPDATE CASCADE ON DELETE SET NULL;
alter table "public"."service_orders" add constraint "service_orders_sales_id_fkey" FOREIGN KEY (sales_id) REFERENCES sales(id) ON UPDATE CASCADE ON DELETE SET NULL;
alter table "public"."service_orders" add constraint "service_orders_invoice_id_fkey" FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON UPDATE CASCADE ON DELETE SET NULL;

alter table "public"."technicians" add constraint "technicians_sales_id_fkey" FOREIGN KEY (sales_id) REFERENCES sales(id) ON UPDATE CASCADE ON DELETE SET NULL;

alter table "public"."service_order_parts" add constraint "service_order_parts_service_order_id_fkey" FOREIGN KEY (service_order_id) REFERENCES service_orders(id) ON UPDATE CASCADE ON DELETE CASCADE;
alter table "public"."service_order_parts" add constraint "service_order_parts_part_id_fkey" FOREIGN KEY (part_id) REFERENCES parts_inventory(id) ON UPDATE CASCADE ON DELETE CASCADE;

alter table "public"."maintenance_schedules" add constraint "maintenance_schedules_equipment_id_fkey" FOREIGN KEY (equipment_id) REFERENCES equipment(id) ON UPDATE CASCADE ON DELETE CASCADE;
alter table "public"."maintenance_schedules" add constraint "maintenance_schedules_assigned_technician_id_fkey" FOREIGN KEY (assigned_technician_id) REFERENCES technicians(id) ON UPDATE CASCADE ON DELETE SET NULL;

alter table "public"."invoices" add constraint "invoices_company_id_fkey" FOREIGN KEY (company_id) REFERENCES companies(id) ON UPDATE CASCADE ON DELETE CASCADE;
alter table "public"."invoices" add constraint "invoices_contact_id_fkey" FOREIGN KEY (contact_id) REFERENCES contacts(id) ON UPDATE CASCADE ON DELETE CASCADE;
alter table "public"."invoices" add constraint "invoices_service_order_id_fkey" FOREIGN KEY (service_order_id) REFERENCES service_orders(id) ON UPDATE CASCADE ON DELETE SET NULL;
alter table "public"."invoices" add constraint "invoices_sales_id_fkey" FOREIGN KEY (sales_id) REFERENCES sales(id) ON UPDATE CASCADE ON DELETE SET NULL;

-- Quotes table for HVAC quotations
create table "public"."quotes" (
    "id" bigint generated by default as identity not null,
    "created_at" timestamp with time zone not null default now(),
    "updated_at" timestamp with time zone not null default now(),
    "quote_number" text not null unique,
    "company_id" bigint not null,
    "contact_id" bigint not null,
    "service_order_id" bigint,
    "title" text not null,
    "description" text,
    "status" text not null default 'draft', -- 'draft', 'sent', 'accepted', 'rejected', 'expired'
    "valid_until" timestamp with time zone not null,
    "subtotal" decimal(10,2) not null default 0,
    "tax_rate" decimal(5,2) not null default 23.00,
    "tax_amount" decimal(10,2) not null default 0,
    "total_amount" decimal(10,2) not null default 0,
    "terms_conditions" text,
    "notes" text,
    "accepted_date" timestamp with time zone,
    "rejected_reason" text,
    "sales_id" bigint,
    "technician_id" bigint
);

alter table "public"."quotes" enable row level security;

-- Quote Items table for quote line items
create table "public"."quote_items" (
    "id" bigint generated by default as identity not null,
    "quote_id" bigint not null,
    "item_type" text not null, -- 'service', 'part', 'equipment', 'labor'
    "description" text not null,
    "quantity" decimal(10,2) not null default 1,
    "unit_price" decimal(10,2) not null,
    "total_price" decimal(10,2) not null,
    "part_id" bigint,
    "equipment_id" bigint,
    "notes" text
);

alter table "public"."quote_items" enable row level security;

-- Quote Templates table for reusable quote templates
create table "public"."quote_templates" (
    "id" bigint generated by default as identity not null,
    "created_at" timestamp with time zone not null default now(),
    "updated_at" timestamp with time zone not null default now(),
    "name" text not null,
    "description" text,
    "category" text, -- 'installation', 'maintenance', 'repair'
    "template_items" jsonb not null, -- template line items
    "default_terms" text,
    "is_active" boolean not null default true,
    "created_by" bigint
);

alter table "public"."quote_templates" enable row level security;

-- Payments table for invoice payments
create table "public"."payments" (
    "id" bigint generated by default as identity not null,
    "created_at" timestamp with time zone not null default now(),
    "invoice_id" bigint not null,
    "payment_date" timestamp with time zone not null default now(),
    "amount" decimal(10,2) not null,
    "payment_method" text not null, -- 'cash', 'card', 'transfer', 'check'
    "reference_number" text,
    "notes" text,
    "processed_by" bigint
);

alter table "public"."payments" enable row level security;

-- Suppliers table for parts suppliers
create table "public"."suppliers" (
    "id" bigint generated by default as identity not null,
    "created_at" timestamp with time zone not null default now(),
    "updated_at" timestamp with time zone not null default now(),
    "name" text not null,
    "contact_person" text,
    "email" text,
    "phone" text,
    "address" text,
    "tax_number" text,
    "payment_terms" text,
    "rating" integer check (rating >= 1 and rating <= 5),
    "notes" text,
    "is_active" boolean not null default true
);

alter table "public"."suppliers" enable row level security;

-- Parts Orders table for ordering parts from suppliers
create table "public"."parts_orders" (
    "id" bigint generated by default as identity not null,
    "created_at" timestamp with time zone not null default now(),
    "updated_at" timestamp with time zone not null default now(),
    "order_number" text not null unique,
    "supplier_id" bigint not null,
    "status" text not null default 'pending', -- 'pending', 'ordered', 'delivered', 'cancelled'
    "order_date" timestamp with time zone not null default now(),
    "expected_delivery" timestamp with time zone,
    "actual_delivery" timestamp with time zone,
    "total_amount" decimal(10,2),
    "notes" text,
    "ordered_by" bigint
);

alter table "public"."parts_orders" enable row level security;

-- Parts Order Items table
create table "public"."parts_order_items" (
    "id" bigint generated by default as identity not null,
    "parts_order_id" bigint not null,
    "part_id" bigint not null,
    "quantity_ordered" integer not null,
    "quantity_received" integer default 0,
    "unit_cost" decimal(10,2) not null,
    "total_cost" decimal(10,2) not null
);

alter table "public"."parts_order_items" enable row level security;

-- Grant permissions for authenticated users
grant delete on table "public"."equipment" to "authenticated";
grant insert on table "public"."equipment" to "authenticated";
grant select on table "public"."equipment" to "authenticated";
grant update on table "public"."equipment" to "authenticated";

grant delete on table "public"."service_orders" to "authenticated";
grant insert on table "public"."service_orders" to "authenticated";
grant select on table "public"."service_orders" to "authenticated";
grant update on table "public"."service_orders" to "authenticated";

grant delete on table "public"."technicians" to "authenticated";
grant insert on table "public"."technicians" to "authenticated";
grant select on table "public"."technicians" to "authenticated";
grant update on table "public"."technicians" to "authenticated";

grant delete on table "public"."parts_inventory" to "authenticated";
grant insert on table "public"."parts_inventory" to "authenticated";
grant select on table "public"."parts_inventory" to "authenticated";
grant update on table "public"."parts_inventory" to "authenticated";

grant delete on table "public"."service_order_parts" to "authenticated";
grant insert on table "public"."service_order_parts" to "authenticated";
grant select on table "public"."service_order_parts" to "authenticated";
grant update on table "public"."service_order_parts" to "authenticated";

grant delete on table "public"."maintenance_schedules" to "authenticated";
grant insert on table "public"."maintenance_schedules" to "authenticated";
grant select on table "public"."maintenance_schedules" to "authenticated";
grant update on table "public"."maintenance_schedules" to "authenticated";

grant delete on table "public"."invoices" to "authenticated";
grant insert on table "public"."invoices" to "authenticated";
grant select on table "public"."invoices" to "authenticated";
grant update on table "public"."invoices" to "authenticated";

-- Grant permissions for service_role
grant delete on table "public"."equipment" to "service_role";
grant insert on table "public"."equipment" to "service_role";
grant references on table "public"."equipment" to "service_role";
grant select on table "public"."equipment" to "service_role";
grant trigger on table "public"."equipment" to "service_role";
grant truncate on table "public"."equipment" to "service_role";
grant update on table "public"."equipment" to "service_role";

grant delete on table "public"."service_orders" to "service_role";
grant insert on table "public"."service_orders" to "service_role";
grant references on table "public"."service_orders" to "service_role";
grant select on table "public"."service_orders" to "service_role";
grant trigger on table "public"."service_orders" to "service_role";
grant truncate on table "public"."service_orders" to "service_role";
grant update on table "public"."service_orders" to "service_role";

grant delete on table "public"."technicians" to "service_role";
grant insert on table "public"."technicians" to "service_role";
grant references on table "public"."technicians" to "service_role";
grant select on table "public"."technicians" to "service_role";
grant trigger on table "public"."technicians" to "service_role";
grant truncate on table "public"."technicians" to "service_role";
grant update on table "public"."technicians" to "service_role";

grant delete on table "public"."parts_inventory" to "service_role";
grant insert on table "public"."parts_inventory" to "service_role";
grant references on table "public"."parts_inventory" to "service_role";
grant select on table "public"."parts_inventory" to "service_role";
grant trigger on table "public"."parts_inventory" to "service_role";
grant truncate on table "public"."parts_inventory" to "service_role";
grant update on table "public"."parts_inventory" to "service_role";

grant delete on table "public"."service_order_parts" to "service_role";
grant insert on table "public"."service_order_parts" to "service_role";
grant references on table "public"."service_order_parts" to "service_role";
grant select on table "public"."service_order_parts" to "service_role";
grant trigger on table "public"."service_order_parts" to "service_role";
grant truncate on table "public"."service_order_parts" to "service_role";
grant update on table "public"."service_order_parts" to "service_role";

grant delete on table "public"."maintenance_schedules" to "service_role";
grant insert on table "public"."maintenance_schedules" to "service_role";
grant references on table "public"."maintenance_schedules" to "service_role";
grant select on table "public"."maintenance_schedules" to "service_role";
grant trigger on table "public"."maintenance_schedules" to "service_role";
grant truncate on table "public"."maintenance_schedules" to "service_role";
grant update on table "public"."maintenance_schedules" to "service_role";

grant delete on table "public"."invoices" to "service_role";
grant insert on table "public"."invoices" to "service_role";
grant references on table "public"."invoices" to "service_role";
grant select on table "public"."invoices" to "service_role";
grant trigger on table "public"."invoices" to "service_role";
grant truncate on table "public"."invoices" to "service_role";
grant update on table "public"."invoices" to "service_role";

-- Create RLS policies for equipment
create policy "Enable read access for authenticated users"
on "public"."equipment"
as permissive
for select
to authenticated
using (true);

create policy "Enable insert for authenticated users only"
on "public"."equipment"
as permissive
for insert
to authenticated
with check (true);

create policy "Enable update for authenticated users only"
on "public"."equipment"
as permissive
for update
to authenticated
using (true)
with check (true);

create policy "Enable delete for authenticated users only"
on "public"."equipment"
as permissive
for delete
to authenticated
using (true);

-- Create RLS policies for service_orders
create policy "Enable read access for authenticated users"
on "public"."service_orders"
as permissive
for select
to authenticated
using (true);

create policy "Enable insert for authenticated users only"
on "public"."service_orders"
as permissive
for insert
to authenticated
with check (true);

create policy "Enable update for authenticated users only"
on "public"."service_orders"
as permissive
for update
to authenticated
using (true)
with check (true);

create policy "Enable delete for authenticated users only"
on "public"."service_orders"
as permissive
for delete
to authenticated
using (true);

-- Create RLS policies for technicians
create policy "Enable read access for authenticated users"
on "public"."technicians"
as permissive
for select
to authenticated
using (true);

create policy "Enable insert for authenticated users only"
on "public"."technicians"
as permissive
for insert
to authenticated
with check (true);

create policy "Enable update for authenticated users only"
on "public"."technicians"
as permissive
for update
to authenticated
using (true)
with check (true);

create policy "Enable delete for authenticated users only"
on "public"."technicians"
as permissive
for delete
to authenticated
using (true);

-- Create RLS policies for parts_inventory
create policy "Enable read access for authenticated users"
on "public"."parts_inventory"
as permissive
for select
to authenticated
using (true);

create policy "Enable insert for authenticated users only"
on "public"."parts_inventory"
as permissive
for insert
to authenticated
with check (true);

create policy "Enable update for authenticated users only"
on "public"."parts_inventory"
as permissive
for update
to authenticated
using (true)
with check (true);

create policy "Enable delete for authenticated users only"
on "public"."parts_inventory"
as permissive
for delete
to authenticated
using (true);

-- Create RLS policies for service_order_parts
create policy "Enable read access for authenticated users"
on "public"."service_order_parts"
as permissive
for select
to authenticated
using (true);

create policy "Enable insert for authenticated users only"
on "public"."service_order_parts"
as permissive
for insert
to authenticated
with check (true);

create policy "Enable update for authenticated users only"
on "public"."service_order_parts"
as permissive
for update
to authenticated
using (true)
with check (true);

create policy "Enable delete for authenticated users only"
on "public"."service_order_parts"
as permissive
for delete
to authenticated
using (true);

-- Create RLS policies for maintenance_schedules
create policy "Enable read access for authenticated users"
on "public"."maintenance_schedules"
as permissive
for select
to authenticated
using (true);

create policy "Enable insert for authenticated users only"
on "public"."maintenance_schedules"
as permissive
for insert
to authenticated
with check (true);

create policy "Enable update for authenticated users only"
on "public"."maintenance_schedules"
as permissive
for update
to authenticated
using (true)
with check (true);

create policy "Enable delete for authenticated users only"
on "public"."maintenance_schedules"
as permissive
for delete
to authenticated
using (true);

-- Create RLS policies for invoices
create policy "Enable read access for authenticated users"
on "public"."invoices"
as permissive
for select
to authenticated
using (true);

create policy "Enable insert for authenticated users only"
on "public"."invoices"
as permissive
for insert
to authenticated
with check (true);

create policy "Enable update for authenticated users only"
on "public"."invoices"
as permissive
for update
to authenticated
using (true)
with check (true);

create policy "Enable delete for authenticated users only"
on "public"."invoices"
as permissive
for delete
to authenticated
using (true);

-- Add indexes and constraints for new tables
CREATE INDEX idx_quotes_company_id ON public.quotes(company_id);
CREATE INDEX idx_quotes_contact_id ON public.quotes(contact_id);
CREATE INDEX idx_quotes_status ON public.quotes(status);
CREATE INDEX idx_quotes_valid_until ON public.quotes(valid_until);
CREATE INDEX idx_quote_items_quote_id ON public.quote_items(quote_id);
CREATE INDEX idx_payments_invoice_id ON public.payments(invoice_id);
CREATE INDEX idx_payments_payment_date ON public.payments(payment_date);
CREATE INDEX idx_suppliers_name ON public.suppliers(name);
CREATE INDEX idx_parts_orders_supplier_id ON public.parts_orders(supplier_id);
CREATE INDEX idx_parts_orders_status ON public.parts_orders(status);

-- Create primary key constraints for new tables
CREATE UNIQUE INDEX quotes_pkey ON public.quotes USING btree (id);
CREATE UNIQUE INDEX quote_items_pkey ON public.quote_items USING btree (id);
CREATE UNIQUE INDEX quote_templates_pkey ON public.quote_templates USING btree (id);
CREATE UNIQUE INDEX payments_pkey ON public.payments USING btree (id);
CREATE UNIQUE INDEX suppliers_pkey ON public.suppliers USING btree (id);
CREATE UNIQUE INDEX parts_orders_pkey ON public.parts_orders USING btree (id);
CREATE UNIQUE INDEX parts_order_items_pkey ON public.parts_order_items USING btree (id);

alter table "public"."quotes" add constraint "quotes_pkey" PRIMARY KEY using index "quotes_pkey";
alter table "public"."quote_items" add constraint "quote_items_pkey" PRIMARY KEY using index "quote_items_pkey";
alter table "public"."quote_templates" add constraint "quote_templates_pkey" PRIMARY KEY using index "quote_templates_pkey";
alter table "public"."payments" add constraint "payments_pkey" PRIMARY KEY using index "payments_pkey";
alter table "public"."suppliers" add constraint "suppliers_pkey" PRIMARY KEY using index "suppliers_pkey";
alter table "public"."parts_orders" add constraint "parts_orders_pkey" PRIMARY KEY using index "parts_orders_pkey";
alter table "public"."parts_order_items" add constraint "parts_order_items_pkey" PRIMARY KEY using index "parts_order_items_pkey";

-- Add foreign key constraints for new tables
alter table "public"."quotes" add constraint "quotes_company_id_fkey" FOREIGN KEY (company_id) REFERENCES companies(id) ON UPDATE CASCADE ON DELETE CASCADE;
alter table "public"."quotes" add constraint "quotes_contact_id_fkey" FOREIGN KEY (contact_id) REFERENCES contacts(id) ON UPDATE CASCADE ON DELETE CASCADE;
alter table "public"."quotes" add constraint "quotes_service_order_id_fkey" FOREIGN KEY (service_order_id) REFERENCES service_orders(id) ON UPDATE CASCADE ON DELETE SET NULL;
alter table "public"."quotes" add constraint "quotes_sales_id_fkey" FOREIGN KEY (sales_id) REFERENCES sales(id) ON UPDATE CASCADE ON DELETE SET NULL;
alter table "public"."quotes" add constraint "quotes_technician_id_fkey" FOREIGN KEY (technician_id) REFERENCES technicians(id) ON UPDATE CASCADE ON DELETE SET NULL;

alter table "public"."quote_items" add constraint "quote_items_quote_id_fkey" FOREIGN KEY (quote_id) REFERENCES quotes(id) ON UPDATE CASCADE ON DELETE CASCADE;
alter table "public"."quote_items" add constraint "quote_items_part_id_fkey" FOREIGN KEY (part_id) REFERENCES parts_inventory(id) ON UPDATE CASCADE ON DELETE SET NULL;
alter table "public"."quote_items" add constraint "quote_items_equipment_id_fkey" FOREIGN KEY (equipment_id) REFERENCES equipment(id) ON UPDATE CASCADE ON DELETE SET NULL;

alter table "public"."payments" add constraint "payments_invoice_id_fkey" FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON UPDATE CASCADE ON DELETE CASCADE;

alter table "public"."parts_orders" add constraint "parts_orders_supplier_id_fkey" FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON UPDATE CASCADE ON DELETE CASCADE;

alter table "public"."parts_order_items" add constraint "parts_order_items_parts_order_id_fkey" FOREIGN KEY (parts_order_id) REFERENCES parts_orders(id) ON UPDATE CASCADE ON DELETE CASCADE;
alter table "public"."parts_order_items" add constraint "parts_order_items_part_id_fkey" FOREIGN KEY (part_id) REFERENCES parts_inventory(id) ON UPDATE CASCADE ON DELETE CASCADE;
